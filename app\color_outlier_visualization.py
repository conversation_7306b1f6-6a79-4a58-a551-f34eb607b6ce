"""
Visualization utilities for 2D color-based outlier detection results.

This module provides functions to visualize 2D color outlier detection results
in LAB color space with scatter plots and decision boundaries.
"""

import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any
from loguru import logger

from color_outlier_detection import ComponentColorData, ColorOutlierDetector


def plot_2d_color_outliers(component_data: List[ComponentColorData],
                          detector: ColorOutlierDetector,
                          title: str | None = None,
                          figsize: tuple = (12, 8)) -> None:
    """
    Plot 2D scatter plot of color outliers in LAB a-b space.
    
    Args:
        component_data: List of component color data with outlier predictions
        detector: The fitted detector
        title: Custom title for the plot
        figsize: Figure size tuple
    """
    if not component_data:
        logger.warning("No component data to plot")
        return
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
    
    # Collect all pixel data
    all_pixels = []
    all_outliers = []
    component_colors = []
    
    # Use different colors for each component
    colors = plt.cm.tab10(np.linspace(0, 1, len(component_data)))
    
    for i, comp_data in enumerate(component_data):
        all_pixels.append(comp_data.ab_pixels)
        all_outliers.append(comp_data.outlier_mask)
        component_colors.extend([colors[i]] * len(comp_data.ab_pixels))
    
    if not all_pixels:
        logger.warning("No pixel data found")
        return
    
    combined_pixels = np.vstack(all_pixels)
    combined_outliers = np.concatenate(all_outliers)
    
    # Plot 1: All pixels colored by component
    ax1.scatter(combined_pixels[:, 0], combined_pixels[:, 1], 
               c=component_colors, alpha=0.6, s=1)
    ax1.set_xlabel('LAB a* channel')
    ax1.set_ylabel('LAB b* channel')
    ax1.set_title('Pixels by Component')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Pixels colored by outlier status
    normal_pixels = combined_pixels[~combined_outliers]
    outlier_pixels = combined_pixels[combined_outliers]
    
    if len(normal_pixels) > 0:
        ax2.scatter(normal_pixels[:, 0], normal_pixels[:, 1], 
                   c='blue', alpha=0.6, s=1, label=f'Normal ({len(normal_pixels)})')
    
    if len(outlier_pixels) > 0:
        ax2.scatter(outlier_pixels[:, 0], outlier_pixels[:, 1], 
                   c='red', alpha=0.8, s=2, label=f'Outliers ({len(outlier_pixels)})')
    
    ax2.set_xlabel('LAB a* channel')
    ax2.set_ylabel('LAB b* channel')
    ax2.set_title(f'Outlier Detection - {detector.name}')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Add overall statistics
    outlier_percentage = np.mean(combined_outliers) * 100
    fig.suptitle(title or f'{detector.name} - {outlier_percentage:.1f}% outlier pixels')
    
    plt.tight_layout()
    plt.show()
    
    # Log statistics
    logger.info(f"{detector.name}: {len(outlier_pixels)}/{len(combined_pixels)} outlier pixels ({outlier_percentage:.1f}%)")


def plot_component_outlier_stats(component_data: List[ComponentColorData],
                                detector: ColorOutlierDetector,
                                figsize: tuple = (10, 6)) -> None:
    """
    Plot component-wise outlier statistics.
    
    Args:
        component_data: List of component color data
        detector: The detector used
        figsize: Figure size tuple
    """
    if not component_data:
        logger.warning("No component data to plot")
        return
    
    component_ids = [comp.component_id for comp in component_data]
    outlier_percentages = [comp.outlier_percentage * 100 for comp in component_data]
    areas = [comp.area for comp in component_data]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize)
    
    # Plot 1: Outlier percentage by component
    bars = ax1.bar(range(len(component_ids)), outlier_percentages)
    
    # Color bars based on threshold
    threshold_pct = detector.outlier_threshold * 100
    for i, (bar, pct) in enumerate(zip(bars, outlier_percentages)):
        if pct > threshold_pct:
            bar.set_color('red')
            bar.set_alpha(0.7)
        else:
            bar.set_color('blue')
            bar.set_alpha(0.7)
    
    ax1.axhline(y=threshold_pct, color='black', linestyle='--', alpha=0.5, 
                label=f'Threshold ({threshold_pct:.0f}%)')
    ax1.set_xlabel('Component Index')
    ax1.set_ylabel('Outlier Percentage (%)')
    ax1.set_title(f'Component Outlier Percentages - {detector.name}')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Set x-tick labels to component IDs
    ax1.set_xticks(range(len(component_ids)))
    ax1.set_xticklabels([f'C{cid}' for cid in component_ids], rotation=45)
    
    # Plot 2: Area vs outlier percentage scatter
    colors = ['red' if pct > threshold_pct else 'blue' for pct in outlier_percentages]
    ax2.scatter(areas, outlier_percentages, c=colors, alpha=0.7)
    ax2.axhline(y=threshold_pct, color='black', linestyle='--', alpha=0.5)
    ax2.set_xlabel('Component Area (pixels)')
    ax2.set_ylabel('Outlier Percentage (%)')
    ax2.set_title('Area vs Outlier Percentage')
    ax2.grid(True, alpha=0.3)
    
    # Add component ID labels
    for i, (area, pct, cid) in enumerate(zip(areas, outlier_percentages, component_ids)):
        ax2.annotate(f'C{cid}', (area, pct), xytext=(5, 5), 
                    textcoords='offset points', fontsize=8, alpha=0.7)
    
    plt.tight_layout()
    plt.show()


def plot_decision_boundary_2d(detector: ColorOutlierDetector,
                             component_data: List[ComponentColorData],
                             resolution: int = 100,
                             figsize: tuple = (10, 8)) -> None:
    """
    Plot decision boundary for 2D color outlier detection.
    
    Args:
        detector: Fitted detector
        component_data: Component color data
        resolution: Grid resolution for decision boundary
        figsize: Figure size tuple
    """
    if not detector.detector or not component_data:
        logger.warning("No fitted detector or component data available")
        return
    
    # Collect all pixel data
    all_pixels = np.vstack([comp.ab_pixels for comp in component_data])
    all_outliers = np.concatenate([comp.outlier_mask for comp in component_data])
    
    # Create a grid for decision boundary
    a_min, a_max = all_pixels[:, 0].min() - 10, all_pixels[:, 0].max() + 10
    b_min, b_max = all_pixels[:, 1].min() - 10, all_pixels[:, 1].max() + 10
    
    aa, bb = np.meshgrid(np.linspace(a_min, a_max, resolution),
                         np.linspace(b_min, b_max, resolution))
    
    grid_points = np.c_[aa.ravel(), bb.ravel()]
    
    try:
        # Get decision function scores for the grid
        if hasattr(detector.detector, 'decision_function'):
            Z = detector.detector.decision_function(grid_points.tolist())
        else:
            # For methods without decision_function, use predict
            Z = detector.detector.predict(grid_points.tolist()).astype(float)
            Z[Z == 1] = 1  # Normal points
            Z[Z == -1] = -1  # Outliers
        
        Z = Z.reshape(aa.shape)
        
        plt.figure(figsize=figsize)
        
        # Plot decision boundary
        plt.contour(aa, bb, Z, levels=[0], colors='black', linestyles='--', linewidths=2)
        plt.contourf(aa, bb, Z, levels=50, alpha=0.3, cmap='RdYlBu')
        
        # Plot data points
        normal_pixels = all_pixels[~all_outliers]
        outlier_pixels = all_pixels[all_outliers]
        
        if len(normal_pixels) > 0:
            plt.scatter(normal_pixels[:, 0], normal_pixels[:, 1], 
                       c='blue', alpha=0.6, s=10, label=f'Normal ({len(normal_pixels)})')
        
        if len(outlier_pixels) > 0:
            plt.scatter(outlier_pixels[:, 0], outlier_pixels[:, 1], 
                       c='red', alpha=0.8, s=15, label=f'Outliers ({len(outlier_pixels)})')
        
        plt.xlabel('LAB a* channel')
        plt.ylabel('LAB b* channel')
        plt.title(f'Decision Boundary - {detector.name}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.colorbar(label='Decision Score')
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        logger.warning(f"Could not plot decision boundary for {detector.name}: {e}")


def compare_color_detectors(results: Dict[str, Any],
                           figsize: tuple = (15, 10)) -> None:
    """
    Compare multiple 2D color outlier detectors side by side.
    
    Args:
        results: Dictionary of detection results from perform_color_outlier_analysis
        figsize: Figure size tuple
    """
    n_detectors = len(results)
    if n_detectors == 0:
        logger.warning("No detection results provided for comparison")
        return
    
    # Calculate grid dimensions
    cols = min(2, n_detectors)
    rows = (n_detectors + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=figsize)
    if n_detectors == 1:
        axes = [axes]
    elif rows == 1:
        axes = [axes]
    else:
        axes = axes.flatten()
    
    for idx, (method_name, result) in enumerate(results.items()):
        ax = axes[idx]
        detector = result['detector']
        component_data = result['component_data']
        
        if not component_data:
            ax.text(0.5, 0.5, f'{detector.name}\nNo data', 
                   ha='center', va='center', transform=ax.transAxes)
            continue
        
        # Collect pixel data
        all_pixels = np.vstack([comp.ab_pixels for comp in component_data])
        all_outliers = np.concatenate([comp.outlier_mask for comp in component_data])
        
        # Plot pixels
        normal_pixels = all_pixels[~all_outliers]
        outlier_pixels = all_pixels[all_outliers]
        
        if len(normal_pixels) > 0:
            ax.scatter(normal_pixels[:, 0], normal_pixels[:, 1], 
                      c='blue', alpha=0.6, s=1, label=f'Normal')
        
        if len(outlier_pixels) > 0:
            ax.scatter(outlier_pixels[:, 0], outlier_pixels[:, 1], 
                      c='red', alpha=0.8, s=2, label=f'Outliers')
        
        ax.set_xlabel('LAB a* channel')
        ax.set_ylabel('LAB b* channel')
        ax.set_title(f'{detector.name}')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add statistics
        outlier_pct = np.mean(all_outliers) * 100
        filtered_count = len(result['filtered_components'])
        total_count = len(component_data)
        
        ax.text(0.02, 0.98, f'{outlier_pct:.1f}% outlier pixels\n{filtered_count}/{total_count} components filtered', 
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Hide unused subplots
    for idx in range(n_detectors, len(axes)):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    plt.show()


def plot_color_summary_table(results: Dict[str, Any]) -> None:
    """
    Create a summary table of 2D color outlier detection results.
    
    Args:
        results: Dictionary of detection results
    """
    if not results:
        logger.warning("No results provided for summary")
        return
    
    # Collect statistics
    summary_data = []
    for method_name, result in results.items():
        detector = result['detector']
        component_data = result['component_data']
        filtered_components = result['filtered_components']
        
        if not component_data:
            continue
        
        # Calculate pixel-level statistics
        all_outliers = np.concatenate([comp.outlier_mask for comp in component_data])
        outlier_pixel_pct = np.mean(all_outliers) * 100
        
        # Component-level statistics
        total_components = len(component_data)
        filtered_count = len(filtered_components)
        filtered_pct = (filtered_count / total_components) * 100 if total_components > 0 else 0
        
        summary_data.append({
            'Method': detector.name,
            'Total Components': total_components,
            'Filtered Components': filtered_count,
            'Filtered %': f'{filtered_pct:.1f}%',
            'Outlier Pixels %': f'{outlier_pixel_pct:.1f}%',
            'Threshold': f'{detector.outlier_threshold:.1%}'
        })
    
    if not summary_data:
        logger.warning("No valid results for summary table")
        return
    
    # Create table plot
    fig, ax = plt.subplots(figsize=(14, len(summary_data) * 0.5 + 2))
    ax.axis('tight')
    ax.axis('off')
    
    # Create table
    headers = ['Method', 'Total Components', 'Filtered Components', 'Filtered %', 'Outlier Pixels %', 'Threshold']
    table_data = []
    table_data.append(headers)
    
    for result in summary_data:
        table_data.append([result[header] for header in headers])
    
    table = ax.table(cellText=table_data[1:], colLabels=table_data[0],
                     cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    
    # Style the table
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#40466e')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    plt.title('2D Color Outlier Detection Summary', fontsize=14, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.show()
    
    # Also log the results
    logger.info("2D Color Outlier Detection Summary:")
    for result in summary_data:
        logger.info(f"  {result['Method']}: {result['Filtered Components']}/{result['Total Components']} "
                   f"components filtered ({result['Filtered %']}), {result['Outlier Pixels %']} outlier pixels")
