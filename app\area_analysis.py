import pathlib
from typing import Dict, Any

import cv2 as cv
import numpy as np
import matplotlib.pyplot as plt
from loguru import logger

from color_filter import ColorFilter, apply_color_filter
from image_colorspace import Image, Mask, create_image
from outlier_detection import create_detector
from outlier_visualization import (
    plot_outlier_histogram,
    plot_decision_scores,
    compare_detectors,
    plot_outlier_summary_table,
)
from color_outlier_detection import perform_color_outlier_analysis
from color_outlier_visualization import (
    plot_2d_color_outliers,
    plot_component_outlier_stats,
    plot_decision_boundary_2d,
    compare_color_detectors,
    plot_color_summary_table,
)


def load_directory_as_image(path: pathlib.Path) -> Image:
    """Load all images in a directory as a single image."""
    images = []
    for file in path.iterdir():
        if file.is_file():
            logger.debug(f"Loading {file}")
            images.append(cv.imread(str(file), cv.IMREAD_UNCHANGED))
    image_bgr = np.concatenate(images, axis=0)
    return create_image(image_bgr)


def make_mask(image: Image) -> Mask:
    """Make a mask from the image."""
    filter = ColorFilter(
        color_space="LAB",
        passthrough=[False, False, False],
        lower=[236, 125, 125],
        upper=[255, 140, 140],
        invert=True,
    )
    return apply_color_filter(image, filter)


def analyze_connected_components(
    mask: Mask, min_area: int = 2000
) -> tuple[np.ndarray, list[int], np.ndarray]:
    """
    Analyze connected components in the mask and filter by minimum area.

    Args:
        mask: Binary mask to analyze
        min_area: Minimum area threshold for components

    Returns:
        tuple: (filtered_mask, list_of_areas, labels)
            - filtered_mask: Mask with only components >= min_area
            - list_of_areas: List of areas for all components >= min_area
            - labels: Label array for all components
    """
    # Convert boolean mask to uint8 for OpenCV
    mask_uint8 = mask.astype(np.uint8) * 255

    # Find connected components
    _, labels, stats, _ = cv.connectedComponentsWithStats(mask_uint8, connectivity=8)

    # Extract areas (stats[:, cv.CC_STAT_AREA] gives area for each component)
    # Skip the first component (index 0) as it's the background
    areas = stats[1:, cv.CC_STAT_AREA]

    # Filter components by minimum area
    valid_components = areas >= min_area
    valid_areas = areas[valid_components].tolist()

    # Create filtered mask with only valid components
    filtered_mask = np.zeros_like(mask, dtype=np.bool_)
    for i, is_valid in enumerate(valid_components, start=1):  # start=1 to skip background
        if is_valid:
            filtered_mask |= labels == i

    logger.info(f"Found {len(areas)} total components, {len(valid_areas)} with area >= {min_area}")

    return filtered_mask, valid_areas, labels


def create_outlier_filtered_mask(
    labels: np.ndarray, outliers: np.ndarray, min_area: int = 2000
) -> np.ndarray:
    """
    Create a mask excluding outlier components.

    Args:
        labels: Label array from connected components
        outliers: Boolean array indicating outliers
        min_area: Minimum area threshold used in original filtering

    Returns:
        Mask with outlier components removed
    """
    # Create mask excluding outliers
    outlier_filtered_mask = np.zeros_like(labels, dtype=np.bool_)
    area_idx = 0

    # Find all components that meet minimum area requirement
    _, _, stats, _ = cv.connectedComponentsWithStats(
        (labels > 0).astype(np.uint8) * 255, connectivity=8
    )
    all_areas = stats[1:, cv.CC_STAT_AREA]  # Skip background

    for i, area in enumerate(all_areas, start=1):  # start=1 to skip background
        if area >= min_area:
            if area_idx < len(outliers) and not outliers[area_idx]:
                outlier_filtered_mask |= labels == i
            area_idx += 1

    return outlier_filtered_mask


def setup_outlier_detectors(contamination: float = 0.1) -> Dict[str, Any]:
    """
    Create and configure all outlier detectors.

    Args:
        contamination: Expected proportion of outliers

    Returns:
        Dictionary of configured detectors
    """
    detectors = {
        "robust_covariance": create_detector("robust_covariance", contamination=contamination),
        "one_class_svm": create_detector("one_class_svm", contamination=contamination),
        "sgd_one_class_svm": create_detector("sgd_one_class_svm", contamination=contamination),
        "isolation_forest": create_detector("isolation_forest", contamination=contamination),
    }

    return detectors


def perform_outlier_analysis(areas: list[int], contamination: float = 0.1) -> Dict[str, Any]:
    """
    Perform outlier detection using multiple methods.

    Args:
        areas: List of component areas
        contamination: Expected proportion of outliers

    Returns:
        Dictionary of fitted detectors
    """
    if len(areas) < 10:
        logger.warning(
            f"Only {len(areas)} components found. Outlier detection may not be reliable."
        )

    detectors = setup_outlier_detectors(contamination)

    # Fit all detectors
    fitted_detectors = {}
    for name, detector in detectors.items():
        try:
            detector.fit(areas)
            fitted_detectors[name] = detector
            logger.info(f"Successfully fitted {detector.name}")
        except Exception as e:
            logger.error(f"Failed to fit {detector.name}: {e}")

    return fitted_detectors


def plot_area_histogram(areas: list[int], title: str = "Component Areas Distribution"):
    """
    Plot histogram of component areas.

    Args:
        areas: List of component areas
        title: Title for the histogram
    """
    if not areas:
        logger.warning("No areas to plot")
        return

    plt.figure(figsize=(10, 6))
    plt.hist(areas, bins=30, alpha=0.7, edgecolor="black")
    plt.xlabel("Area (pixels)")
    plt.ylabel("Frequency")
    plt.title(title)
    plt.grid(True, alpha=0.3)

    # Add statistics to the plot
    mean_area = float(np.mean(areas))
    median_area = float(np.median(areas))
    plt.axvline(mean_area, color="red", linestyle="--", label=f"Mean: {mean_area:.1f}")
    plt.axvline(median_area, color="green", linestyle="--", label=f"Median: {median_area:.1f}")
    plt.legend()

    logger.info(
        f"Area statistics: Mean={mean_area:.1f}, Median={median_area:.1f}, "
        f"Min={min(areas)}, Max={max(areas)}, Count={len(areas)}"
    )

    plt.show()


def main(path: pathlib.Path, contamination: float = 0.15, color_outlier_threshold: float = 0.5):
    """
    Main analysis function with 1D area and 2D color outlier detection.

    Args:
        path: Path to directory containing images
        contamination: Expected proportion of outliers (default: 0.15 = 15%)
        color_outlier_threshold: Threshold for component filtering based on color outliers
                               (default: 0.5 = remove components with >50% outlier pixels)
    """
    # Load and display original image
    image = load_directory_as_image(path)
    cv.imshow("image", image.BGR[::8, ::8])
    logger.debug(
        f"Image statistics: {np.min(image.BGR, axis=(0, 1))}, {np.max(image.BGR, axis=(0, 1))}"
    )
    cv.waitKey(1)

    # Create color mask
    mask = make_mask(image)
    logger.debug(
        f"Image statistics: {np.min(image.LAB, axis=(0, 1))}, {np.max(image.LAB, axis=(0, 1))}"
    )

    # Show original masked image
    masked = cv.bitwise_and(image.BGR, image.BGR, mask=mask.astype(np.uint8))
    cv.imshow("masked", masked[::8, ::8])
    cv.waitKey(1)

    # Perform connected components analysis and filter by area
    filtered_mask, areas, labels = analyze_connected_components(mask)

    # Show filtered mask (only components >= min_area pixels)
    filtered_masked = cv.bitwise_and(image.BGR, image.BGR, mask=filtered_mask.astype(np.uint8))
    cv.imshow("filtered_masked", filtered_masked[::8, ::8])
    cv.waitKey(1)

    if not areas:
        logger.warning("No components found with sufficient area")
        cv.waitKey(0)
        cv.destroyAllWindows()
        return

    # Plot basic histogram
    plot_area_histogram(areas, "Distribution of Component Areas (≥2000 pixels)")

    # Perform 1D area-based outlier detection if we have enough components
    if len(areas) >= 5:
        logger.info("=== 1D AREA-BASED OUTLIER DETECTION ===")
        logger.info(f"Performing area-based outlier detection on {len(areas)} components...")

        # Fit all area-based outlier detectors
        detectors = perform_outlier_analysis(areas, contamination=contamination)

        if detectors:
            # Compare all detectors
            compare_detectors(areas, detectors)
            plot_outlier_summary_table(areas, detectors)

            # Show detailed results for each detector
            for name, detector in detectors.items():
                outliers = detector.predict()
                scores = detector.decision_function()

                # Plot histogram with outliers highlighted
                plot_outlier_histogram(areas, outliers, detector.name)

                # Plot decision scores
                plot_decision_scores(areas, scores, outliers, detector.name)

                # Create and show outlier-filtered image for this detector
                outlier_filtered_mask = create_outlier_filtered_mask(labels, outliers)
                outlier_filtered_image = cv.bitwise_and(
                    image.BGR, image.BGR, mask=outlier_filtered_mask.astype(np.uint8)
                )
                cv.imshow(f"area_outlier_filtered_{name}", outlier_filtered_image[::8, ::8])
                cv.waitKey(1)
        else:
            logger.warning("No area-based outlier detectors were successfully fitted")
    else:
        logger.warning(
            f"Only {len(areas)} components found. Skipping area-based outlier detection (need ≥5)"
        )

    # Perform 2D color-based outlier detection
    logger.info("=== 2D COLOR-BASED OUTLIER DETECTION ===")
    logger.info("Performing 2D color outlier detection on LAB a-b channels...")

    try:
        color_results = perform_color_outlier_analysis(
            image=image,
            labels=labels,
            areas=areas,
            min_area=2000,
            contamination=contamination,
            outlier_threshold=color_outlier_threshold,
        )

        if color_results:
            # Compare all color detectors
            compare_color_detectors(color_results)
            plot_color_summary_table(color_results)

            # Show detailed results for each color detector
            for method_name, result in color_results.items():
                detector = result["detector"]
                component_data = result["component_data"]
                filtered_mask = result["filtered_mask"]

                if component_data:
                    # Plot 2D color scatter plots
                    plot_2d_color_outliers(component_data, detector)

                    # Plot component statistics
                    plot_component_outlier_stats(component_data, detector)

                    # Plot decision boundary (if possible)
                    plot_decision_boundary_2d(detector, component_data)

                    # Show color-filtered image
                    color_filtered_image = cv.bitwise_and(
                        image.BGR, image.BGR, mask=filtered_mask.astype(np.uint8)
                    )
                    cv.imshow(
                        f"color_outlier_filtered_{method_name}", color_filtered_image[::8, ::8]
                    )
                    cv.waitKey(1)
        else:
            logger.warning("No 2D color outlier detectors were successfully fitted")

    except Exception as e:
        logger.error(f"Failed to perform 2D color outlier detection: {e}")

    cv.waitKey(0)
    cv.destroyAllWindows()


if __name__ == "__main__":
    main(pathlib.Path("../data/oranje_bolletjes/2025-07-29_20.03.28/"))
