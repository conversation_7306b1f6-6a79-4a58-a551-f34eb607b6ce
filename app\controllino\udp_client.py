# udp_client.py
import asyncio
import struct


class UdpClient:
    def __init__(self, ip: str, port: int, local_port: int = 0):
        self.remote = (ip, port)
        self.local_port = local_port
        self.transport = None
        self.protocol = None
        self.seq = 0
        self.pending = {}  # seq -> Future

    async def connect(self):
        loop = asyncio.get_running_loop()
        self.transport, self.protocol = await loop.create_datagram_endpoint(
            lambda: _UdpProtocol(self.pending),
            local_addr=("0.0.0.0", self.local_port),
            remote_addr=self.remote,
        )

    async def close(self):
        if self.transport:
            self.transport.close()
            self.transport = None

    async def request(self, body: bytes, timeout: float = 1.0) -> bytes | None:
        """
        Sends a request and waits for a matching response.
        """
        if not self.transport:
            raise RuntimeError("UDP client not connected")

        # Generate sequence number (2 bytes)
        self.seq = (self.seq + 1) & 0xFFFF
        seq_bytes = struct.pack("!H", self.seq)
        msg = seq_bytes + body

        loop = asyncio.get_running_loop()
        fut = loop.create_future()
        self.pending[self.seq] = fut

        self.transport.sendto(msg, self.remote)

        try:
            return await asyncio.wait_for(fut, timeout)
        except asyncio.TimeoutError:
            self.pending.pop(self.seq, None)
            return None


class _UdpProtocol(asyncio.DatagramProtocol):
    def __init__(self, pending):
        self.pending = pending

    def datagram_received(self, data, addr):
        if len(data) < 2:
            return
        seq = struct.unpack("!H", data[:2])[0]
        payload = data[2:]
        fut = self.pending.pop(seq, None)
        if fut and not fut.done():
            fut.set_result(payload)

    def error_received(self, exc):
        print(f"UDP error: {exc}")
