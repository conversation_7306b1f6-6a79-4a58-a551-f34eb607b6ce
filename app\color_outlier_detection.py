"""
2D Color-based outlier detection system for LAB color space.

This module extends the outlier detection system to work with 2D color features
extracted from connected components in LAB color space.
"""

import numpy as np
import cv2 as cv
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from loguru import logger

from outlier_detection import BaseOutlierDetector, create_detector
from image_colorspace import Image


@dataclass
class ComponentColorData:
    """Container for component color data and metadata."""

    component_id: int
    area: int
    ab_pixels: np.ndarray  # Nx2 array of (a, b) coordinates
    outlier_mask: np.ndarray | None = None  # Boolean mask for outlier pixels
    outlier_percentage: float = 0.0


class ColorOutlierDetector:
    """2D color-based outlier detector for connected components."""

    def __init__(
        self,
        method: str = "isolation_forest",
        contamination: float = 0.1,
        outlier_threshold: float = 0.5,
    ):
        """
        Initialize the 2D color outlier detector.

        Args:
            method: Outlier detection method to use
            contamination: Expected proportion of outlier pixels
            outlier_threshold: Threshold for component filtering (0.0-1.0)
                             Components with >threshold outlier pixels are filtered
        """
        self.method = method
        self.contamination = contamination
        self.outlier_threshold = outlier_threshold
        self.detector = None
        self.fitted_data = None
        self.component_data: List[ComponentColorData] = []

    def extract_component_colors(
        self, image: Image, labels: np.ndarray, areas: List[int], min_area: int = 2000
    ) -> List[ComponentColorData]:
        """
        Extract 2D color features from connected components.

        Args:
            image: Image with LAB color space data
            labels: Connected component labels
            areas: List of component areas
            min_area: Minimum area threshold

        Returns:
            List of ComponentColorData objects
        """
        component_data = []
        area_idx = 0

        # Get LAB image
        lab_image = image.LAB

        # Find all components that meet minimum area requirement
        _, _, stats, _ = cv.connectedComponentsWithStats(
            (labels > 0).astype(np.uint8) * 255, connectivity=8
        )
        all_areas = stats[1:, cv.CC_STAT_AREA]  # Skip background

        for component_id, area in enumerate(all_areas, start=1):
            if area >= min_area:
                # Get mask for this component
                component_mask = labels == component_id

                # Extract a and b channel values for this component
                a_values = lab_image[component_mask, 1]  # 'a' channel
                b_values = lab_image[component_mask, 2]  # 'b' channel

                # Combine into Nx2 array
                ab_pixels = np.column_stack([a_values, b_values])

                component_data.append(
                    ComponentColorData(component_id=component_id, area=area, ab_pixels=ab_pixels)
                )

                logger.debug(
                    f"Component {component_id}: {area} pixels, "
                    f"color range a=[{a_values.min():.1f}, {a_values.max():.1f}], "
                    f"b=[{b_values.min():.1f}, {b_values.max():.1f}]"
                )

                area_idx += 1

        logger.info(f"Extracted color data from {len(component_data)} components")
        return component_data

    def fit_and_predict(
        self, component_data: List[ComponentColorData]
    ) -> List[ComponentColorData]:
        """
        Fit outlier detector to 2D color data and predict outliers.

        Args:
            component_data: List of component color data

        Returns:
            Updated component data with outlier predictions
        """
        if not component_data:
            logger.warning("No component data provided for outlier detection")
            return component_data

        # Combine all pixel data from all components
        all_pixels = []
        component_pixel_counts = []

        for comp_data in component_data:
            all_pixels.append(comp_data.ab_pixels)
            component_pixel_counts.append(len(comp_data.ab_pixels))

        if not all_pixels:
            logger.warning("No pixel data found in components")
            return component_data

        # Concatenate all pixel data
        combined_pixels = np.vstack(all_pixels)
        logger.info(
            f"Fitting outlier detector to {len(combined_pixels)} pixels from {len(component_data)} components"
        )

        # Create and fit detector
        try:
            self.detector = create_detector(self.method, contamination=self.contamination)
            self.detector.fit(combined_pixels.tolist())  # Convert to list for compatibility
            self.fitted_data = combined_pixels

            # Predict outliers for all pixels
            all_outliers = self.detector.predict(combined_pixels.tolist())

            # Split outlier predictions back to components
            start_idx = 0
            for i, comp_data in enumerate(component_data):
                end_idx = start_idx + component_pixel_counts[i]
                comp_outliers = all_outliers[start_idx:end_idx]

                # Update component data
                comp_data.outlier_mask = comp_outliers
                comp_data.outlier_percentage = np.mean(comp_outliers)

                logger.info(
                    f"Component {comp_data.component_id}: "
                    f"{comp_data.outlier_percentage:.1%} outlier pixels "
                    f"({np.sum(comp_outliers)}/{len(comp_outliers)})"
                )

                start_idx = end_idx

        except Exception as e:
            logger.error(f"Failed to fit {self.method} detector: {e}")
            # Set empty outlier data
            for comp_data in component_data:
                comp_data.outlier_mask = np.zeros(len(comp_data.ab_pixels), dtype=bool)
                comp_data.outlier_percentage = 0.0

        self.component_data = component_data
        return component_data

    def filter_components(
        self, component_data: List[ComponentColorData]
    ) -> Tuple[List[ComponentColorData], List[ComponentColorData]]:
        """
        Filter components based on outlier percentage threshold.

        Args:
            component_data: List of component color data with outlier predictions

        Returns:
            Tuple of (valid_components, filtered_components)
        """
        valid_components = []
        filtered_components = []

        for comp_data in component_data:
            if comp_data.outlier_percentage <= self.outlier_threshold:
                valid_components.append(comp_data)
            else:
                filtered_components.append(comp_data)

        logger.info(
            f"Component filtering results: {len(valid_components)} valid, "
            f"{len(filtered_components)} filtered (threshold: {self.outlier_threshold:.1%})"
        )

        return valid_components, filtered_components

    def create_filtered_mask(
        self, labels: np.ndarray, valid_components: List[ComponentColorData]
    ) -> np.ndarray:
        """
        Create a mask with only valid (non-outlier) components.

        Args:
            labels: Original connected component labels
            valid_components: List of components to keep

        Returns:
            Boolean mask with valid components
        """
        filtered_mask = np.zeros_like(labels, dtype=bool)

        for comp_data in valid_components:
            component_mask = labels == comp_data.component_id
            filtered_mask |= component_mask

        return filtered_mask

    @property
    def name(self) -> str:
        """Return detector name."""
        if self.detector:
            return f"2D Color {self.detector.name}"
        return f"2D Color {self.method}"


def setup_color_detectors(
    contamination: float = 0.1, outlier_threshold: float = 0.5
) -> Dict[str, ColorOutlierDetector]:
    """
    Create multiple 2D color outlier detectors.

    Args:
        contamination: Expected proportion of outlier pixels
        outlier_threshold: Component filtering threshold

    Returns:
        Dictionary of configured detectors
    """
    methods = ["robust_covariance", "one_class_svm", "sgd_one_class_svm", "isolation_forest"]
    detectors = {}

    for method in methods:
        detectors[method] = ColorOutlierDetector(
            method=method, contamination=contamination, outlier_threshold=outlier_threshold
        )

    return detectors


def perform_color_outlier_analysis(
    image: Image,
    labels: np.ndarray,
    areas: List[int],
    min_area: int = 2000,
    contamination: float = 0.1,
    outlier_threshold: float = 0.5,
) -> Dict[str, Any]:
    """
    Perform complete 2D color outlier analysis.

    Args:
        image: Image with LAB color space data
        labels: Connected component labels
        areas: List of component areas
        min_area: Minimum area threshold
        contamination: Expected proportion of outlier pixels
        outlier_threshold: Component filtering threshold

    Returns:
        Dictionary with analysis results
    """
    results = {}

    # Create detectors
    detectors = setup_color_detectors(contamination, outlier_threshold)

    for method_name, detector in detectors.items():
        try:
            # Extract color data
            component_data = detector.extract_component_colors(image, labels, areas, min_area)

            if not component_data:
                logger.warning(f"No components found for {method_name}")
                continue

            # Fit and predict
            component_data = detector.fit_and_predict(component_data)

            # Filter components
            valid_components, filtered_components = detector.filter_components(component_data)

            # Create filtered mask
            filtered_mask = detector.create_filtered_mask(labels, valid_components)

            results[method_name] = {
                "detector": detector,
                "component_data": component_data,
                "valid_components": valid_components,
                "filtered_components": filtered_components,
                "filtered_mask": filtered_mask,
            }

            logger.info(
                f"{detector.name}: {len(valid_components)}/{len(component_data)} components kept"
            )

        except Exception as e:
            logger.error(f"Failed to analyze with {method_name}: {e}")

    return results
