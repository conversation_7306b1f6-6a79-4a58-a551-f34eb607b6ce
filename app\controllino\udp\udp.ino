#include <SPI.h>
#include <Ethernet.h>
#include <EthernetUdp.h>

// Ethernet setup
byte mac[] = {0xDE, 0xAD, 0xBE, 0xEF, 0xFE, 0xED};
IPAddress ip(192, 168, 255, 177);
unsigned int localPort = 8888;
EthernetUDP Udp;
char buffer[UDP_TX_PACKET_MAX_SIZE];
char reply[] = "0123456789";

// // Encoder setup
// const int IN0 = 18;
// const int IN1 = 19;
volatile long encoderCount = 5;
// volatile int lastA = 0, lastB = 0;
// void isrA()
// {
//   int A = digitalRead(IN0);
//   int B = digitalRead(IN1);
//   // Simple quadrature: direction by B relative to A
//   if (A != lastA)
//     encoderCount += (A == B) ? +1 : -1;
//   lastA = A;
//   lastB = B;
// }
// void isrB()
// {
//   int A = digitalRead(IN0);
//   int B = digitalRead(IN1);
//   if (B != lastB)
//     encoderCount += (A != B) ? +1 : -1;
//   lastA = A;
//   lastB = B;
// }

void setup()
{
  Ethernet.begin(mac, ip);
  Udp.begin(localPort);
  for (int i = 2; i < 10; i++)
  {
    pinMode(i, OUTPUT);
  }

  // pinMode(IN0, INPUT);
  // pinMode(IN1, INPUT);
  // attachInterrupt(digitalPinToInterrupt(IN0), isrA, CHANGE);
  // attachInterrupt(digitalPinToInterrupt(IN1), isrB, CHANGE);
}

void loop()
{
  int packetSize = Udp.parsePacket();
  if (packetSize)
  {

    int len = Udp.read(buffer, sizeof(buffer));
    if (len > 0)
    {
      Udp.beginPacket(Udp.remoteIP(), Udp.remotePort());
      // Udp.write(reply, 5+2); // doesn't work; no reply
      Udp.write(buffer, 5 + 2); // works; sends back the first 5 characters
      Udp.endPacket();
    }
  }
}
