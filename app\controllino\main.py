# test_udp.py
import asyncio
import time
from udp_client import UdpClient

CONTROLLINO_IP = "***************"
PORT = 8888


async def main():
    client = UdpClient(CONTROLLINO_IP, PORT)
    await client.connect()

    for i in range(5):
        body = f"Hello {i}".encode()
        start = time.perf_counter()
        response = await client.request(body, timeout=0.5)
        end = time.perf_counter()
        print(f"Request: {body!r}  Response: {response!r}  Time: {(end - start) * 1000:.1f}ms")

    await client.close()


if __name__ == "__main__":
    asyncio.run(main())
