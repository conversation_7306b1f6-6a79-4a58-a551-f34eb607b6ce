# Outlier Detection System for Area Analysis

This document describes the comprehensive modular outlier detection system implemented for the area analysis pipeline. The system provides both 1D area-based and 2D color-based outlier detection using multiple scikit-learn methods.

## Overview

The outlier detection system consists of six main modules:

### Core Modules
1. **`outlier_detection.py`** - 1D area-based detection algorithms and common interface
2. **`outlier_visualization.py`** - Visualization utilities for 1D results
3. **`color_outlier_detection.py`** - 2D color-based detection in LAB space
4. **`color_outlier_visualization.py`** - Visualization utilities for 2D color results
5. **`area_analysis.py`** - Integrated pipeline with both detection types
6. **Test modules** - Comprehensive testing for both systems

## Features

### Detection Approaches

#### 1D Area-Based Outlier Detection
Identifies components with anomalous areas compared to the overall distribution.

#### 2D Color-Based Outlier Detection
Analyzes pixel-level color patterns in LAB color space to identify components with unusual color characteristics.

### Implemented Detection Methods

Both 1D and 2D systems use the same underlying algorithms:

1. **Robust Covariance (EllipticEnvelope)**
   - Assumes data is Gaussian distributed
   - Fits an ellipse to central data points
   - Good for normally distributed measurements

2. **One-Class SVM (RBF kernel)**
   - Uses Support Vector Machine with RBF kernel
   - Learns decision boundary around normal data
   - Effective for non-linear patterns

3. **One-Class SVM (SGD)**
   - Linear One-Class SVM with Stochastic Gradient Descent
   - Scales linearly with number of samples
   - Faster for large datasets

4. **Isolation Forest**
   - Ensemble method using random trees
   - Isolates anomalies through recursive partitioning
   - Excellent for high-dimensional data

### Common Interface

All detectors implement a common interface:

```python
from outlier_detection import create_detector

# Create detector
detector = create_detector('isolation_forest', contamination=0.1)

# Fit to data
detector.fit(areas)

# Predict outliers (True = outlier, False = normal)
outliers = detector.predict()

# Get outlier scores
scores = detector.decision_function()
```

### 2D Color-Based Detection Features

- **LAB Color Space Analysis** - Extracts 'a' and 'b' channel values from each component
- **Pixel-Level Detection** - Analyzes individual pixel colors within components
- **Component-Level Filtering** - Filters entire components based on outlier pixel percentage
- **Configurable Thresholds** - Adjustable outlier percentage threshold for component filtering

### Visualization Features

#### 1D Area-Based Visualizations
- **Histogram with outlier highlighting** - Shows distribution with outliers in different colors
- **Decision score plots** - Visualizes outlier scores vs. area values
- **Comparison plots** - Side-by-side comparison of multiple methods
- **Summary tables** - Statistical summary of detection results

#### 2D Color-Based Visualizations
- **2D scatter plots** - Shows pixel distribution in LAB a-b color space
- **Component color mapping** - Different colors for each component
- **Outlier highlighting** - Red markers for outlier pixels
- **Decision boundaries** - Visual representation of detection boundaries
- **Component statistics** - Bar charts showing outlier percentages per component
- **Area vs outlier correlation** - Scatter plots correlating component size with outlier rate

## Usage

### Basic Usage

#### 1D Area-Based Detection

```python
from outlier_detection import create_detector
from outlier_visualization import plot_outlier_histogram

# Your area data
areas = [100, 120, 110, 115, 105, 500, 95, 108, 112, 600]

# Create and fit detector
detector = create_detector('isolation_forest', contamination=0.2)
detector.fit(areas)

# Get results
outliers = detector.predict()
scores = detector.decision_function()

# Visualize
plot_outlier_histogram(areas, outliers, detector.name)
```

#### 2D Color-Based Detection

```python
from color_outlier_detection import ColorOutlierDetector, perform_color_outlier_analysis
from color_outlier_visualization import plot_2d_color_outliers

# Create detector
detector = ColorOutlierDetector(
    method='isolation_forest',
    contamination=0.1,
    outlier_threshold=0.5  # Filter components with >50% outlier pixels
)

# Extract color features from components
component_data = detector.extract_component_colors(image, labels, areas, min_area=2000)

# Fit and predict outliers
component_data = detector.fit_and_predict(component_data)

# Filter components based on outlier percentage
valid_components, filtered_components = detector.filter_components(component_data)

# Visualize 2D color results
plot_2d_color_outliers(component_data, detector)
```

#### Complete Analysis (Both Methods)

```python
from color_outlier_detection import perform_color_outlier_analysis
from color_outlier_visualization import compare_color_detectors

# Perform complete 2D color analysis
color_results = perform_color_outlier_analysis(
    image=image,
    labels=labels,
    areas=areas,
    contamination=0.1,
    outlier_threshold=0.5
)

# Compare all methods
compare_color_detectors(color_results)
```

### Integrated Pipeline

The main `area_analysis.py` script now includes both 1D and 2D outlier detection:

```bash
pipenv run python area_analysis.py
```

This will:
1. Load and process images
2. Perform connected components analysis
3. Filter by minimum area (2000 pixels)
4. **1D Area-Based Detection:**
   - Run all area-based outlier detection methods
   - Display comparison visualizations
   - Show area-filtered images for each method
5. **2D Color-Based Detection:**
   - Extract LAB a-b color features from each component
   - Run all color-based outlier detection methods
   - Calculate outlier pixel percentages per component
   - Filter components based on configurable threshold
   - Display 2D color visualizations and decision boundaries
   - Show color-filtered images for each method

### Configuration

#### Core Parameters

- **`contamination`** (0.0-0.5): Expected proportion of outliers in the data (default: 0.15)
- **`min_area`** (int): Minimum component area threshold (default: 2000 pixels)
- **`color_outlier_threshold`** (0.0-1.0): Component filtering threshold for 2D color detection (default: 0.5)

#### 2D Color-Specific Parameters

- **`outlier_threshold`**: Percentage of outlier pixels required to filter a component
  - 0.5 = Remove components where >50% of pixels are color outliers
  - 0.3 = Remove components where >30% of pixels are color outliers
  - 0.8 = Remove components where >80% of pixels are color outliers

#### Method-Specific Parameters
Each detector has additional tuning options for fine-grained control.

## File Structure

```
app/
├── outlier_detection.py              # 1D area-based detection algorithms
├── outlier_visualization.py          # 1D visualization utilities
├── color_outlier_detection.py        # 2D color-based detection algorithms
├── color_outlier_visualization.py    # 2D color visualization utilities
├── area_analysis.py                  # Main integrated analysis pipeline
├── test_outlier_detection.py         # 1D system test script
├── test_color_outlier_detection.py   # 2D system test script
└── README_outlier_detection.md       # This documentation
```

## Dependencies

The system requires these packages (automatically installed via Pipfile):

- `scikit-learn` - Machine learning algorithms
- `matplotlib` - Plotting and visualization
- `numpy` - Numerical computations
- `opencv-python` - Image processing
- `loguru` - Logging

## Testing

### 1D Area-Based System Testing

```bash
pipenv run python test_outlier_detection.py
```

This creates synthetic area data with known outliers and tests all 1D detection methods.

### 2D Color-Based System Testing

```bash
pipenv run python test_color_outlier_detection.py
```

This creates synthetic LAB color components with distinct color patterns and tests:
- Color feature extraction from components
- 2D outlier detection in LAB a-b space
- Component filtering based on outlier pixel percentages
- Visualization of 2D color results

### Complete System Testing

Run the main pipeline on real data:

```bash
pipenv run python area_analysis.py
```

This performs both 1D and 2D outlier detection on actual image data.

## Performance Notes

- **Robust Covariance**: Fast, works well with Gaussian data
- **One-Class SVM (RBF)**: Moderate speed, good for non-linear patterns
- **One-Class SVM (SGD)**: Fastest, linear complexity
- **Isolation Forest**: Good balance of speed and accuracy

For large datasets (>1000 components), consider using SGD One-Class SVM or Isolation Forest.

## Integration with Existing Pipeline

The comprehensive outlier detection system is fully integrated with the existing area analysis pipeline:

### Pipeline Flow

1. **Color filtering** → Creates binary mask from LAB color space
2. **Connected components** → Identifies individual components with labels
3. **Area filtering** → Removes components < min_area (2000 pixels)
4. **1D Area-based outlier detection** → Identifies components with anomalous areas
5. **2D Color-based outlier detection** → Analyzes LAB a-b color patterns within components
6. **Component filtering** → Removes components based on:
   - Area-based outlier classification
   - Color-based outlier pixel percentage
7. **Visualization** → Shows both 1D and 2D results with filtered images

### Key Benefits

- **Dual Detection Approach**: Combines area and color-based anomaly detection
- **Spatial Preservation**: Maintains component spatial relationships for accurate image filtering
- **Configurable Thresholds**: Adjustable parameters for different data characteristics
- **Comprehensive Visualization**: Multiple plot types for understanding detection results
- **Modular Design**: Easy to extend with additional detection methods
- **Real-time Feedback**: Visual confirmation of filtering results

### Use Cases

- **Area-based filtering**: Remove components that are too large or too small compared to the distribution
- **Color-based filtering**: Remove components with unusual color characteristics in LAB space
- **Combined filtering**: Use both approaches for robust anomaly detection
- **Quality control**: Identify and remove defective or contaminated regions in images

The system is designed to handle cases where no outliers are detected (framework is in place for when outliers exist) and provides meaningful feedback about the detection process.
