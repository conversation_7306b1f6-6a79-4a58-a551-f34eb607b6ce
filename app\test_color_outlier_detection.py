#!/usr/bin/env python3
"""
Test script for the 2D color-based outlier detection system.
"""

import numpy as np
import cv2 as cv
from loguru import logger

from image_colorspace import create_image
from color_outlier_detection import ColorOutlierDetector, ComponentColorData
from color_outlier_visualization import plot_2d_color_outliers, plot_component_outlier_stats


def create_synthetic_lab_image_with_components():
    """Create a synthetic LAB image with distinct color components for testing."""
    
    # Create a 200x200 image
    height, width = 200, 200
    
    # Create BGR image first
    bgr_image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Component 1: Blue region (top-left)
    bgr_image[20:80, 20:80] = [255, 100, 100]  # Blue in BGR
    
    # Component 2: Green region (top-right) 
    bgr_image[20:80, 120:180] = [100, 255, 100]  # Green in BGR
    
    # Component 3: Red region (bottom-left)
    bgr_image[120:180, 20:80] = [100, 100, 255]  # Red in BGR
    
    # Component 4: Yellow region (bottom-right) - potential outlier
    bgr_image[120:180, 120:180] = [100, 255, 255]  # Yellow in BGR
    
    # Component 5: Purple region (center) - potential outlier
    bgr_image[90:110, 90:110] = [255, 100, 255]  # Purple in BGR
    
    # Convert to Image object with LAB
    image = create_image(bgr_image)
    
    # Create synthetic labels for connected components
    labels = np.zeros((height, width), dtype=np.int32)
    labels[20:80, 20:80] = 1      # Component 1
    labels[20:80, 120:180] = 2    # Component 2  
    labels[120:180, 20:80] = 3    # Component 3
    labels[120:180, 120:180] = 4  # Component 4
    labels[90:110, 90:110] = 5    # Component 5
    
    # Calculate areas
    areas = []
    for comp_id in range(1, 6):
        area = np.sum(labels == comp_id)
        areas.append(area)
    
    logger.info(f"Created synthetic image with {len(areas)} components")
    logger.info(f"Component areas: {areas}")
    
    return image, labels, areas


def test_color_outlier_detection():
    """Test the 2D color outlier detection system with synthetic data."""
    
    logger.info("Creating synthetic LAB image with color components...")
    image, labels, areas = create_synthetic_lab_image_with_components()
    
    # Display the LAB color ranges for each component
    for comp_id in range(1, 6):
        mask = (labels == comp_id)
        if np.any(mask):
            lab_values = image.LAB[mask]
            l_range = (lab_values[:, 0].min(), lab_values[:, 0].max())
            a_range = (lab_values[:, 1].min(), lab_values[:, 1].max())
            b_range = (lab_values[:, 2].min(), lab_values[:, 2].max())
            logger.info(f"Component {comp_id}: L={l_range}, a={a_range}, b={b_range}")
    
    # Test different detection methods
    methods = ['robust_covariance', 'isolation_forest', 'one_class_svm']
    
    for method in methods:
        logger.info(f"\n=== Testing {method} ===")
        
        try:
            # Create detector
            detector = ColorOutlierDetector(
                method=method,
                contamination=0.2,  # Expect 20% outliers
                outlier_threshold=0.3  # Filter components with >30% outlier pixels
            )
            
            # Extract color data
            component_data = detector.extract_component_colors(image, labels, areas, min_area=100)
            
            if not component_data:
                logger.warning(f"No component data extracted for {method}")
                continue
            
            # Fit and predict
            component_data = detector.fit_and_predict(component_data)
            
            # Filter components
            valid_components, filtered_components = detector.filter_components(component_data)
            
            logger.info(f"{detector.name}: {len(valid_components)}/{len(component_data)} components kept")
            
            # Show results
            if component_data:
                plot_2d_color_outliers(component_data, detector)
                plot_component_outlier_stats(component_data, detector)
            
        except Exception as e:
            logger.error(f"Failed to test {method}: {e}")
    
    return True


def test_component_color_extraction():
    """Test the color extraction functionality specifically."""
    
    logger.info("Testing component color extraction...")
    image, labels, areas = create_synthetic_lab_image_with_components()
    
    detector = ColorOutlierDetector()
    component_data = detector.extract_component_colors(image, labels, areas, min_area=100)
    
    logger.info(f"Extracted data from {len(component_data)} components:")
    
    for comp_data in component_data:
        a_values = comp_data.ab_pixels[:, 0]
        b_values = comp_data.ab_pixels[:, 1]
        
        logger.info(f"Component {comp_data.component_id}: {comp_data.area} pixels")
        logger.info(f"  a-channel: min={a_values.min():.1f}, max={a_values.max():.1f}, mean={a_values.mean():.1f}")
        logger.info(f"  b-channel: min={b_values.min():.1f}, max={b_values.max():.1f}, mean={b_values.mean():.1f}")
    
    return component_data


if __name__ == "__main__":
    logger.info("Starting 2D color outlier detection tests...")
    
    # Test color extraction
    test_component_color_extraction()
    
    # Test full outlier detection
    test_color_outlier_detection()
    
    logger.info("2D color outlier detection tests completed!")
